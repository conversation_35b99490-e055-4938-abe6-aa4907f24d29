package com.juzifenqi.plus.module.order.repository.dao;

import com.juzifenqi.plus.dto.req.admin.OrderBillQueryReq;
import com.juzifenqi.plus.module.order.repository.po.PlusOrderBillPo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 会员订单对账记录-Dao
 *
 * <AUTHOR>
 * @date 2023-07-13 14:33:23
 */
@Mapper
public interface IPlusOrderBillMapper {

    /**
     * 新增返回ID
     */
    Integer savePlusOrderBill(PlusOrderBillPo plusOrderBill);

    /**
     * 新增
     */
    Integer insertPlusOrderBill(@Param("plusOrderBill") PlusOrderBillPo plusOrderBill);

    /**
     * 删除
     */
    Integer deletePlusOrderBill(@Param("id") Integer id);

    /**
     * 更新
     */
    Integer updatePlusOrderBill(@Param("plusOrderBill") PlusOrderBillPo plusOrderBill);

    /**
     * 更新
     */
    Integer updatePlusOrderBillByOrderSn(@Param("plusOrderBill") PlusOrderBillPo plusOrderBill);

    /**
     * Load查询
     */
    PlusOrderBillPo loadPlusOrderBill(@Param("id") Integer id);

    /**
     * 按会员单号查询
     */
    PlusOrderBillPo selectByOrderSn(@Param("orderSn") String orderSn);

    /**
     * 获取入账重试通知列表
     */
    List<PlusOrderBillPo> selectInComeRetryList(@Param("size") Integer size);

    /**
     * 获取出账重试通知列表
     */
    List<PlusOrderBillPo> selectOutComeRetryList(@Param("size") Integer size);

    /**
     * 批量修改入账通知状态
     */
    int updateInStateBatch(@Param("ids") List<Integer> ids, @Param("state") Integer state);

    /**
     * 批量修改出账通知状态
     */
    int updateOutStateBatch(@Param("ids") List<Integer> ids, @Param("state") Integer state);

    /**
     * 获取黑卡合同生成重试列表
     */
    List<PlusOrderBillPo> selectContractReTryList(@Param("size") Integer size);

    /**
     * 批量修改合同状态为-已发起
     */
    int updateContractStateBatch(@Param("ids") List<Integer> ids, @Param("state") Integer state);

    /**
     * 获取合同上传重试列表
     */
    List<PlusOrderBillPo> selectContractUploadReTryList(@Param("size") Integer size);

    /**
     * 批量修改合同上传状态
     */
    int updateContractUploadStateBatch(@Param("ids") List<Integer> ids,
            @Param("state") Integer state);

    /**
     * 获取划扣成功回调时未获取到bill记录导致没有走入账逻辑的数据（6个小时以前的数据）
     */
    List<PlusOrderBillPo> selectDeductIncomeRetryList(@Param("limit") Integer limit);

    /**
     * 分页查询Data 只查询入账成功的
     */
    List<PlusOrderBillPo> pageList(OrderBillQueryReq req);

    /**
     * 分页查询Count 只查询入账成功的
     */
    Integer pageListCount(OrderBillQueryReq req);
}
