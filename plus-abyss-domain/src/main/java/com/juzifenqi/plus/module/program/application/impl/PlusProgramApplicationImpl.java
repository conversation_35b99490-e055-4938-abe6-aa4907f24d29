package com.juzifenqi.plus.module.program.application.impl;

import com.juzifenqi.plus.constants.RedisConstantPrefix;
import com.juzifenqi.plus.dto.req.admin.program.CopyProgramReq;
import com.juzifenqi.plus.dto.req.admin.program.PlusProgramEditQueryReq;
import com.juzifenqi.plus.dto.req.admin.program.PlusProgramQueryReq;
import com.juzifenqi.plus.dto.req.admin.program.PriceQueryReq;
import com.juzifenqi.plus.dto.resp.admin.program.ProgramQueryReq;
import com.juzifenqi.plus.enums.PlusProgramLogNodeEnum;
import com.juzifenqi.plus.exception.PlusAbyssException;
import com.juzifenqi.plus.module.common.entity.PageResultEntity;
import com.juzifenqi.plus.module.program.application.IPlusProgramApplication;
import com.juzifenqi.plus.module.program.application.validator.PlusProgramValidator;
import com.juzifenqi.plus.module.program.model.IPlusPriceConfigModel;
import com.juzifenqi.plus.module.program.model.IPlusProductModel;
import com.juzifenqi.plus.module.program.model.IPlusProfitModel;
import com.juzifenqi.plus.module.program.model.IPlusProgramModel;
import com.juzifenqi.plus.module.program.model.IPlusProgramQueryModel;
import com.juzifenqi.plus.module.program.model.entity.PlusConfigEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusProgramEditPriceEntity;
import com.juzifenqi.plus.module.program.model.entity.price.DefaultPriceDetailEntity;
import com.juzifenqi.plus.module.program.model.entity.price.DiffPriceDetailEntity;
import com.juzifenqi.plus.module.program.model.entity.price.PlusProgramPriceListEntity;
import com.juzifenqi.plus.module.program.model.event.ProgramDetailEntity;
import com.juzifenqi.plus.module.program.model.event.price.SaveDefaultPriceEvent;
import com.juzifenqi.plus.module.program.model.event.price.SaveDiffPriceEvent;
import com.juzifenqi.plus.module.program.model.event.program.CreateProgramEditPriceEvent;
import com.juzifenqi.plus.module.program.model.event.program.MultiplexChannelProgramEvent;
import com.juzifenqi.plus.module.program.model.event.program.SavePlusProgramEvent;
import com.juzifenqi.plus.module.program.model.event.program.UpProgramEvent;
import com.juzifenqi.plus.module.program.repository.entity.PlusProgramEntity;
import com.juzifenqi.plus.module.program.repository.entity.PlusProgramListEntity;
import com.juzifenqi.plus.pool.PlusThreadPool;
import com.juzifenqi.plus.utils.RedisLock;
import com.juzifenqi.plus.utils.RedisUtils;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 方案
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/3 14:57
 */
@Service
@Slf4j
public class PlusProgramApplicationImpl implements IPlusProgramApplication {

    @Autowired
    private IPlusProgramModel      programModel;
    @Autowired
    private IPlusPriceConfigModel  plusPriceConfigModel;
    @Autowired
    private PlusProgramValidator   validator;
    @Autowired
    private RedisLock              redisLock;
    @Resource
    private IPlusProfitModel       profitModel;
    @Resource
    private IPlusProductModel      plusProductModel;
    @Resource
    private RedisUtils             redisUtils;
    @Resource
    private IPlusProgramQueryModel queryModel;

    @Override
    public PageResultEntity<PlusProgramEditPriceEntity> getProgramEditPriceList(
            PlusProgramEditQueryReq req) {
        PageResultEntity<PlusProgramEditPriceEntity> result = new PageResultEntity<>();
        result.setList(programModel.getProgramEditPriceList(req));
        result.setCount(programModel.countProgramEditPrice(req));
        return result;
    }

    @Override
    public void addProgramEditPrice(CreateProgramEditPriceEvent event) {
        programModel.addProgramEditPrice(event);
    }

    @Override
    public void executeProgramEditPriceTask() {
        programModel.executeProgramEditPriceTask();
    }

    @Override
    public void saveDiffPrice(SaveDiffPriceEvent event) {
        validator.checkDiffParams(event);
        String key =
                RedisConstantPrefix.ADD_DIFF_PRICE + event.getChannelId() + event.getConfigId();
        if (!redisLock.lock(key, "1", 3)) {
            throw new PlusAbyssException("请勿重复保存差异化定价配置");
        }
        plusPriceConfigModel.saveDiffPrice(event);
    }

    @Override
    public void editDiffPrice(SaveDiffPriceEvent event) {
        validator.checkDiffParams(event);
        String key = RedisConstantPrefix.UPD_DIFF_PRICE + event.getId();
        if (!redisLock.lock(key, "1", 3)) {
            throw new PlusAbyssException("请勿重复编辑差异化定价配置");
        }
        plusPriceConfigModel.editDiffPrice(event);
    }

    @Override
    public DiffPriceDetailEntity getDiffDetail(Integer id) {
        return plusPriceConfigModel.getDiffDetail(id);
    }

    @Override
    public void addDefaultPrice(SaveDefaultPriceEvent event) {
        validator.checkDefaultParams(event);
        String key = RedisConstantPrefix.ADD_DEFAULT_PRICE + event.getChannelId();
        if (!redisLock.lock(key, "1", 3)) {
            throw new PlusAbyssException("请勿重复新增默认方案配置");
        }
        plusPriceConfigModel.addDefaultPrice(event);
    }

    @Override
    public void editDefaultPrice(SaveDefaultPriceEvent event) {
        validator.checkDefaultParams(event);
        String key = RedisConstantPrefix.UPD_DEFAULT_PRICE + event.getChannelId();
        if (!redisLock.lock(key, "1", 3)) {
            throw new PlusAbyssException("请勿重复编辑默认方案配置");
        }
        plusPriceConfigModel.editDefaultPrice(event);
    }

    @Override
    public DefaultPriceDetailEntity getDefaultPriceDetail(Integer id) {
        return plusPriceConfigModel.getDefaultPriceDetail(id);
    }

    @Override
    public PageResultEntity<PlusProgramPriceListEntity> getPriceList(PriceQueryReq req) {
        return plusPriceConfigModel.getPriceList(req);
    }

    @Override
    public void reloadProductCache() {
        profitModel.reloadAllHalfPriceProductToCache();
        profitModel.reloadHalfPriceProductLimit6ToCache();
    }

    @Override
    public PageResultEntity<PlusProgramEntity> getMemberPlusProgramAll(PlusProgramQueryReq req) {
        List<PlusProgramEntity> memberPlusProgramAll = programModel.getMemberPlusProgramAll(req);
        PageResultEntity<PlusProgramEntity> result = new PageResultEntity<>();
        result.setList(memberPlusProgramAll);
        result.setCount(memberPlusProgramAll.size());
        return result;
    }

    @Override
    public Boolean updateProgrammeUpAndDown(UpProgramEvent event) {
        Boolean result;
        String content;
        String eventType;
        if (event.getStatus() == 1) {
            validator.checkProgramEffective(event.getId());
            result = programModel.upAct(String.valueOf(event.getId()));
            if (Boolean.TRUE.equals(result)) {
                plusProductModel.updateMemberPlusGoodsState(String.valueOf(event.getId()), 1);
                //缓存处理比较耗时，导致页面一直显示进度条，这里采用异步处理
                PlusThreadPool.threadPool.execute(() -> {
                    profitModel.reloadAllHalfPriceProductToCache();
                    profitModel.reloadHalfPriceProductLimit6ToCache();
                    profitModel.saveCache(event.getId());
                });
            }
            content = event.getOptUserName() + "上架了该方案";
            eventType = PlusProgramLogNodeEnum.LOG_NODE_OPEN.getName();
        } else {
            result = programModel.downAct(String.valueOf(event.getId()));
            if (Boolean.TRUE.equals(result)) {
                plusProductModel.updateMemberPlusGoodsState(String.valueOf(event.getId()), 0);
                profitModel.deleteCache(event.getId());
            }
            content = event.getOptUserName() + "下架了该方案";
            eventType = PlusProgramLogNodeEnum.LOG_NODE_DOWN.getName();
        }
        //  桔享Plus方案添加记录日志
        programModel.saveMemberPlusProgramLog(event.getId(), event.getOptUserId(),
                event.getOptUserName(), eventType, content);
        return result;
    }

    @Override
    public void handlePlusBasicProfitsCache() {
        try {
            List<PlusProgramEntity> programEntities = programModel.getUpProgramList();
            boolean productNeedHandle = false;
            List<Integer> needHandleProgramId = new ArrayList<>(programEntities.size());
            for (PlusProgramEntity program : programEntities) {
                String redisKeyProgramId =
                        RedisConstantPrefix.MEMBER_PROGRAM_ID_KEY + program.getId();
                //看会员方案的缓存是否过期（50分钟），过期才去更新相关缓存
                if (Boolean.FALSE.equals(redisUtils.hasKey(redisKeyProgramId))) {
                    needHandleProgramId.add(program.getId());
                    productNeedHandle = true;
                }
            }
            if (productNeedHandle) {
                reloadProductCache();
            }
            for (Integer programId : needHandleProgramId) {
                profitModel.saveCache(programId);
            }
        } catch (Exception e) {
            log.info("定时处理方案ID和方案基础权益缓存任务", e);
        }
    }

    @Override
    public List<PlusConfigEntity> loadPlusConf() {
        return programModel.loadPlusConf();
    }

    @Override
    public PageResultEntity<PlusProgramListEntity> getProgramList(ProgramQueryReq req) {
        return programModel.getProgramList(req);
    }

    @Override
    public void addProgram(SavePlusProgramEvent event) {
        validator.checkSaveProgram(event);
        programModel.addProgram(event);
    }

    @Override
    public void editProgram(SavePlusProgramEvent event) {
        validator.checkSaveProgram(event);
        programModel.editProgram(event);
    }

    @Override
    public void programEffective(SavePlusProgramEvent event) {
        validator.checkProgramEffective(event.getId());
        programModel.programEffective(event);
    }

    /**
     * 复用渠道方案
     */
    @Override
    public void multiplexChannelProgram(MultiplexChannelProgramEvent event) {
        // 验证入参
        validator.checkMultiplexChannelProgramParams(event);
        programModel.multiplexChannelProgram(event);
    }

    @Override
    public ProgramDetailEntity getProgramDetail(Integer id) {
        return programModel.getProgramDetail(id);
    }

    /**
     * 复制方案
     */
    @Override
    public void copyProgram(CopyProgramReq req) {
        // 验证入参
        validator.checkCopyProgramParams(req);
        programModel.copyProgram(req);
    }

    /**
     * 通过方案id查询方案信息
     */
    @Override
    public PlusProgramEntity getPlusProgramById(Integer programId) {
        return queryModel.getById(programId);
    }
}
