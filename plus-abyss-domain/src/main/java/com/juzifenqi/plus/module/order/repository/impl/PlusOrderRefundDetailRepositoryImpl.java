package com.juzifenqi.plus.module.order.repository.impl;

import com.juzifenqi.plus.module.order.model.contract.IPlusOrderRefundDetailRepository;
import com.juzifenqi.plus.module.order.model.contract.entity.refund.PlusOrderRefundDetailEntity;
import com.juzifenqi.plus.module.order.repository.converter.IPlusOrderRefundDetailRepositoryConverter;
import com.juzifenqi.plus.module.order.repository.dao.IPlusOrderRefundDetailMapper;
import com.juzifenqi.plus.module.order.repository.po.PlusOrderRefundDetailPo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class PlusOrderRefundDetailRepositoryImpl implements IPlusOrderRefundDetailRepository {

    private final IPlusOrderRefundDetailRepositoryConverter convert = IPlusOrderRefundDetailRepositoryConverter.instance;

    @Autowired
    private IPlusOrderRefundDetailMapper plusOrderRefundDetailMapper;


    @Override
    public List<PlusOrderRefundDetailEntity> getRefundDetailByRefundInfoId(Long refundInfoId) {
        if (refundInfoId == null){
            return null;
        }
        List<PlusOrderRefundDetailPo> list =  plusOrderRefundDetailMapper.getRefundDetailByRefundInfoId(refundInfoId);
        return convert.toEntityList(list);
    }
}
