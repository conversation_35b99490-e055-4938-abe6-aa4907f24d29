<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.order.repository.dao.IPlusOrderRefundDetailMapper">

    <resultMap id="plusOrderDetailInfo" type="com.juzifenqi.plus.module.order.repository.po.PlusOrderRefundDetailPo" >
        <result column="id" property="id" />
        <result column="refund_info_id" property="refundInfoId" />
        <result column="original_pay_serial_no" property="originalPaySerialNo"/>
        <result column="refund_serial_no" property="refundSerialNo" />
        <result column="pay_serial_no" property="paySerialNo" />
        <result column="refund_state" property="refundState" />
        <result column="refund_amount" property="refundAmount"/>
        <result column="pay_callback_time" property="payCallbackTime"/>
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `refund_info_id`,
        `original_pay_serial_no`,
        `refund_serial_no`,
        `pay_serial_no`,
        `refund_state`,
        `refund_amount`,
        `pay_callback_time`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="batchInsert" parameterType="com.juzifenqi.plus.module.order.repository.po.PlusOrderRefundDetailPo" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO plus_order_refund_detail (
        `refund_info_id`,
        `original_pay_serial_no`,
        `refund_serial_no`,
        `pay_serial_no`,
        `refund_state`,
        `refund_amount`
        )
        VALUES
        <foreach collection="list" index="index" item="l" separator=",">
            (
            #{l.refundInfoId},
            #{l.originalPaySerialNo},
            #{l.refundSerailNo},
            #{l.paySerialNo},
            #{l.refundState},
            #{l.refundAmount}
            )
        </foreach>
    </insert>

    <update id="updateStateByIds">
        update plus_order_refund_detail set refund_state = #{refundState} where id
        <foreach collection="ids" item="id" index="index" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateDetail">
        update plus_order_refund_detail
        set
        <if test="paySerialNo != null">`pay_serial_no`= #{paySerialNo},</if>
        refund_state = #{refundState}
        where id = #{id}
    </update>

    <select id="getDetailByRefundSerialNo" resultMap="plusOrderDetailInfo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_order_refund_detail
        WHERE `refund_serial_no` = #{refundSerialNo}
    </select>

    <select id="getDetailsByInfoIdAndState" resultMap="plusOrderDetailInfo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_order_refund_detail
        WHERE `refund_info_id` = #{refundInfoId}
    </select>
</mapper>
