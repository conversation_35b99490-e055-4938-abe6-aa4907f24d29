<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.order.repository.dao.IPlusOrderBillMapper">

    <resultMap id="PlusOrderBill" type="com.juzifenqi.plus.module.order.repository.po.PlusOrderBillPo" >
        <result column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="channel_id" property="channelId" />
        <result column="config_id" property="configId" />
        <result column="program_id" property="programId" />
        <result column="order_sn" property="orderSn" />
        <result column="serial_number" property="serialNumber" />
        <result column="supplier_order_sn" property="supplierOrderSn" />
        <result column="trail_state" property="trailState" />
        <result column="sign_state" property="signState" />
        <result column="sign_retry_count" property="signRetryCount" />
        <result column="in_supplier" property="inSupplier" />
        <result column="in_amount" property="inAmount" />
        <result column="in_state" property="inState" />
        <result column="in_retry_count" property="inRetryCount" />
        <result column="in_time" property="inTime" />
        <result column="in_remark" property="inRemark" />
        <result column="out_supplier" property="outSupplier" />
        <result column="out_amount" property="outAmount" />
        <result column="out_state" property="outState" />
        <result column="out_retry_count" property="outRetryCount" />
        <result column="out_time" property="outTime" />
        <result column="contract_upload_state" property="contractUploadState" />
        <result column="contract_upload_retry_count" property="contractUploadRetryCount" />
        <result column="out_remark" property="outRemark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `user_id`,
        `channel_id`,
        `config_id`,
        `program_id`,
        `order_sn`,
        `serial_number`,
        `supplier_order_sn`,
        `trail_state`,
        `sign_state`,
        `sign_retry_count`,
        `in_supplier`,
        `in_amount`,
        `in_state`,
        `in_retry_count`,
        `in_time`,
        `in_remark`,
        `out_supplier`,
        `out_amount`,
        `out_state`,
        `out_retry_count`,
        `out_time`,
        `out_remark`,
        `contract_upload_retry_count`,
        `contract_upload_state`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="savePlusOrderBill" parameterType="com.juzifenqi.plus.module.order.repository.po.PlusOrderBillPo" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO plus_order_bill (
        `user_id`,
        `channel_id`,
        `config_id`,
        `program_id`,
        `order_sn`,
        `serial_number`,
        `supplier_order_sn`,
        `trail_state`,
        `sign_state`,
        `sign_retry_count`,
        `in_supplier`,
        `in_amount`,
        `in_state`,
        `in_retry_count`,
        `in_time`,
        `in_remark`,
        `out_supplier`,
        `out_amount`,
        `out_state`,
        `out_retry_count`,
        `out_time`,
        `out_remark`,
        `contract_upload_retry_count`,
        `contract_upload_state`,
        `create_time`,
        `update_time`
        )
        VALUES(
        #{userId},
        #{channelId},
        #{configId},
        #{programId},
        #{orderSn},
        #{serialNumber},
        #{supplierOrderSn},
        #{trailState},
        #{signState},
        #{signRetryCount},
        #{inSupplier},
        #{inAmount},
        #{inState},
        #{inRetryCount},
        #{inTime},
        #{inRemark},
        #{outSupplier},
        #{outAmount},
        #{outState},
        #{outRetryCount},
        #{outTime},
        #{outRemark},
        #{contractUploadRetryCount},
        #{contractUploadState},
        NOW(),
        #{updateTime}
        )
    </insert>


    <insert id="insertPlusOrderBill" parameterType="com.juzifenqi.plus.module.order.repository.po.PlusOrderBillPo">
        INSERT INTO plus_order_bill (
        `user_id`,
        `channel_id`,
        `config_id`,
        `program_id`,
        `order_sn`,
        `serial_number`,
        `supplier_order_sn`,
        `trail_state`,
        `sign_state`,
        `sign_retry_count`,
        `in_supplier`,
        `in_amount`,
        `in_state`,
        `in_retry_count`,
        `in_time`,
        `in_remark`,
        `out_supplier`,
        `out_amount`,
        `out_state`,
        `out_retry_count`,
        `out_time`,
        `out_remark`,
        `contract_upload_retry_count`,
        `contract_upload_state`,
        `create_time`,
        `update_time`
        )
        VALUES(
        #{plusOrderBill.userId},
        #{plusOrderBill.channelId},
        #{plusOrderBill.configId},
        #{plusOrderBill.programId},
        #{plusOrderBill.orderSn},
        #{plusOrderBill.serialNumber},
        #{plusOrderBill.supplierOrderSn},
        #{plusOrderBill.trailState},
        #{plusOrderBill.signState},
        #{plusOrderBill.signRetryCount},
        #{plusOrderBill.inSupplier},
        #{plusOrderBill.inAmount},
        #{plusOrderBill.inState},
        #{plusOrderBill.inRetryCount},
        #{plusOrderBill.inTime},
        #{plusOrderBill.inRemark},
        #{plusOrderBill.outSupplier},
        #{plusOrderBill.outAmount},
        #{plusOrderBill.outState},
        #{plusOrderBill.outRetryCount},
        #{plusOrderBill.outTime},
        #{plusOrderBill.outRemark},
        #{contractUploadRetryCount},
        #{contractUploadState},
        NOW(),
        #{plusOrderBill.updateTime}
        )
    </insert>



    <delete id="deletePlusOrderBill" parameterType="java.lang.Integer">
        DELETE FROM plus_order_bill
        WHERE `id` = #{id}
    </delete>

    <update id="updatePlusOrderBill" parameterType="com.juzifenqi.plus.module.order.repository.po.PlusOrderBillPo" >
        UPDATE plus_order_bill
        SET
        <if test="plusOrderBill.serialNumber != null">`serial_number`= #{plusOrderBill.serialNumber},</if>
        <if test="plusOrderBill.supplierOrderSn != null">`supplier_order_sn`= #{plusOrderBill.supplierOrderSn},</if>
        <if test="plusOrderBill.trailState != null">`trail_state`= #{plusOrderBill.trailState},</if>
        <if test="plusOrderBill.signState != null">`sign_state`= #{plusOrderBill.signState},</if>
        <if test="plusOrderBill.signRetryCount != null">`sign_retry_count`= #{plusOrderBill.signRetryCount},</if>
        <if test="plusOrderBill.inSupplier != null">`in_supplier`= #{plusOrderBill.inSupplier},</if>
        <if test="plusOrderBill.inAmount != null">`in_amount`= #{plusOrderBill.inAmount},</if>
        <if test="plusOrderBill.inState != null">`in_state`= #{plusOrderBill.inState},</if>
        <if test="plusOrderBill.inRetryCount != null">`in_retry_count`= #{plusOrderBill.inRetryCount},</if>
        <if test="plusOrderBill.inTime != null">`in_time`= #{plusOrderBill.inTime},</if>
        <if test="plusOrderBill.inRemark != null">`in_remark`= #{plusOrderBill.inRemark},</if>
        <if test="plusOrderBill.outSupplier != null">`out_supplier`= #{plusOrderBill.outSupplier},</if>
        <if test="plusOrderBill.outAmount != null">`out_amount`= #{plusOrderBill.outAmount},</if>
        <if test="plusOrderBill.outState != null">`out_state`= #{plusOrderBill.outState},</if>
        <if test="plusOrderBill.outRetryCount != null">`out_retry_count`= #{plusOrderBill.outRetryCount},</if>
        <if test="plusOrderBill.outTime != null">`out_time`= #{plusOrderBill.outTime},</if>
        <if test="plusOrderBill.outRemark != null">`out_remark`= #{plusOrderBill.outRemark},</if>
        <if test="plusOrderBill.contractUploadState != null">`contract_upload_state`= #{plusOrderBill.contractUploadState},</if>
        <if test="plusOrderBill.contractUploadRetryCount != null">`contract_upload_retry_count`= #{plusOrderBill.contractUploadRetryCount},</if>
        update_time = now()
        WHERE `id` = #{plusOrderBill.id}
    </update>


    <update id="updatePlusOrderBillByOrderSn" parameterType="com.juzifenqi.plus.module.order.repository.po.PlusOrderBillPo" >
        UPDATE plus_order_bill
        SET
        <if test="plusOrderBill.serialNumber != null">`serial_number`= #{plusOrderBill.serialNumber},</if>
        <if test="plusOrderBill.supplierOrderSn != null">`supplier_order_sn`= #{plusOrderBill.supplierOrderSn},</if>
        <if test="plusOrderBill.trailState != null">`trail_state`= #{plusOrderBill.trailState},</if>
        <if test="plusOrderBill.signState != null">`sign_state`= #{plusOrderBill.signState},</if>
        <if test="plusOrderBill.signRetryCount != null">`sign_retry_count`= #{plusOrderBill.signRetryCount},</if>
        <if test="plusOrderBill.inSupplier != null">`in_supplier`= #{plusOrderBill.inSupplier},</if>
        <if test="plusOrderBill.inAmount != null">`in_amount`= #{plusOrderBill.inAmount},</if>
        <if test="plusOrderBill.inState != null">`in_state`= #{plusOrderBill.inState},</if>
        <if test="plusOrderBill.inRetryCount != null">`in_retry_count`= #{plusOrderBill.inRetryCount},</if>
        <if test="plusOrderBill.inTime != null">`in_time`= #{plusOrderBill.inTime},</if>
        <if test="plusOrderBill.inRemark != null">`in_remark`= #{plusOrderBill.inRemark},</if>
        <if test="plusOrderBill.outSupplier != null">`out_supplier`= #{plusOrderBill.outSupplier},</if>
        <if test="plusOrderBill.outAmount != null">`out_amount`= #{plusOrderBill.outAmount},</if>
        <if test="plusOrderBill.outState != null">`out_state`= #{plusOrderBill.outState},</if>
        <if test="plusOrderBill.outRetryCount != null">`out_retry_count`= #{plusOrderBill.outRetryCount},</if>
        <if test="plusOrderBill.outTime != null">`out_time`= #{plusOrderBill.outTime},</if>
        <if test="plusOrderBill.outRemark != null">`out_remark`= #{plusOrderBill.outRemark},</if>
        <if test="plusOrderBill.contractUploadState != null">`contract_upload_state`= #{plusOrderBill.contractUploadState},</if>
        <if test="plusOrderBill.contractUploadRetryCount != null">`contract_upload_retry_count`= #{plusOrderBill.contractUploadRetryCount},</if>
        update_time = now()
        WHERE `order_sn` = #{plusOrderBill.orderSn}
    </update>


    <select id="loadPlusOrderBill" parameterType="java.lang.Integer" resultMap="PlusOrderBill">
        SELECT <include refid="Base_Column_List" />
        FROM plus_order_bill
        WHERE `id` = #{id}
    </select>

    <select id="selectByOrderSn" resultMap="PlusOrderBill">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_order_bill
        WHERE order_sn = #{orderSn} order by id desc limit 1
    </select>


    <select id="selectInComeRetryList" resultMap="PlusOrderBill">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_order_bill
        WHERE in_state = 2 and in_retry_count &lt; 3 order by id limit
        #{size}
    </select>


    <select id="selectOutComeRetryList" resultMap="PlusOrderBill">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_order_bill
        WHERE out_state = 2 and out_retry_count &lt; 3 order by id
        limit #{size}
    </select>

    <update id="updateInStateBatch">
        update plus_order_bill set `in_state` = #{state}
        where
        <foreach collection="ids" item="id" open="id in (" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateOutStateBatch">
        update plus_order_bill set `out_state` = #{state}
        where
        <foreach collection="ids" item="id" open="id in (" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="selectContractReTryList" resultMap="PlusOrderBill">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_order_bill
        WHERE sign_state = 4 and sign_retry_count &lt; 5 order by id
        limit #{size}
    </select>

    <update id="updateContractStateBatch">
        update plus_order_bill set `sign_state` = #{state}
        where
        <foreach collection="ids" item="id" open="id in (" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="selectContractUploadReTryList" resultMap="PlusOrderBill">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_order_bill
        WHERE contract_upload_state = 4 and contract_upload_retry_count &lt; 5 order by id
        limit #{size}
    </select>

    <update id="updateContractUploadStateBatch">
        update plus_order_bill set `contract_upload_state` = #{state}
        where
        <foreach collection="ids" item="id" open="id in (" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="selectDeductIncomeRetryList" resultMap="PlusOrderBill">
        select <include refid="Base_Column_List"/> from plus_order_bill
        where in_state is null and trail_state = 1 and create_time &lt;= date_sub(now(), interval 6 hour)
        <if test="limit!=null">
            limit #{limit}
        </if>
    </select>

    <select id="pageList" parameterType="com.juzifenqi.plus.dto.req.admin.OrderBillQueryReq" resultMap="PlusOrderBill">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_order_bill
        <where>
            <if test="configId!=null">
                and config_id = #{configId}
            </if>
            <if test="userId!=null">
                and user_id = #{userId}
            </if>
            <if test="plusOrderSn!=null and plusOrderSn!=''">
                and order_sn = #{plusOrderSn}
            </if>
            <if test="inSupplierId!=null">
                and in_supplier = #{inSupplierId}
            </if>
            <if test="outSupplierId!=null">
                and out_supplier = #{outSupplierId}
            </if>
            <if test="inStartTime!=null and inEndTime!=null">
                and in_time >= #{inStartTime} and in_time &lt;= #{inEndTime}
            </if>
        </where>
        order by id desc LIMIT #{startPage}, #{pageSize}
    </select>

    <select id="pageListCount" parameterType="com.juzifenqi.plus.dto.req.admin.OrderBillQueryReq" resultType="java.lang.Integer">
        SELECT count(*)
        FROM plus_order_bill
        <where>
            <if test="configId!=null">
                and config_id = #{configId}
            </if>
            <if test="userId!=null">
                and user_id = #{userId}
            </if>
            <if test="plusOrderSn!=null and plusOrderSn!=''">
                and order_sn = #{plusOrderSn}
            </if>
            <if test="inSupplierId!=null">
                and in_supplier = #{inSupplierId}
            </if>
            <if test="outSupplierId!=null">
                and out_supplier = #{outSupplierId}
            </if>
            <if test="inStartTime!=null and inEndTime!=null">
                and in_time >= #{inStartTime} and in_time &lt;= #{inEndTime}
            </if>
        </where>
    </select>
</mapper>
